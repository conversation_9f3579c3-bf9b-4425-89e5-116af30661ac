import { PoolClient } from "pg";
import {db }from "../config/database";
import { GetAllPropertiesResponseDTO } from "../dto/property/GetAllPropertiesResponseDTO";
import { PaginationDTO } from "../dto/property/PaginationDTO";
import { PropertyDTO } from "../dto/property/PropertyDTO";
import { StatusCountDTO } from "../dto/property/StatusCountDTO";
import { PropertyQueries } from "../utils/database/queries/PropertyQueries";

export class PropertyRepository {
  async getFilteredProperties(
    statusId: number | null,
    propertyTypeId: number | null,
    locationId: number | null,
    listingType: number | null,
    limit: number,
    currentPage: number
  ): Promise<{ rows: any[], statusCountsRows: any[] }> {
    const params = [
      statusId,
      propertyTypeId,
      locationId,
      listingType,
      limit,
      currentPage,
    ];
    const { rows } = await db.query(
      PropertyQueries.GET_FILTERED_PROPERTIES,
      params
    );
    const { rows: statusCountsRows } = await db.query(
      PropertyQueries.GET_PROPERTY_STATUS_COUNTS,
      [propertyTypeId, locationId, listingType]
    );
    return { rows, statusCountsRows };
  }

  async getPropertyById(id: number) {
    const { rows } = await db.query(PropertyQueries.GET_PROPERTY_DETAIL_BY_ID, [
      id,
    ]);
    return rows[0] as PropertyDTO;
  }

  async savePropertyWithAssets(
    data: any,
    imageKeys: string[],
    features: string[] | string,
    statusId: number,
    loginId: number,
    client: PoolClient
  ): Promise<PropertyDTO> {
    // -------- Step 1: Create or Update Property --------
    const fields = [
      "id",
      "code",
      "name",
      "local",
      "agencyId",
      "propertyTypeId",
      "apartmentTypeId",
      "totalRooms",
      "locationId",
      "address",
      "currencyId",
      "price",
      "size",
      "permitNo",
      "parking",
      "swimmingPools",
      "gym",
      "startDate",
      "statusId",
      "createdBy",
      "isFeatured",
      "isVerified",
      "adminNote",
      "expiryDate",
      "listingType",
      "completionStatus",
      "ownershipTypeId",
      "slug",
      "metaTitle",
      "metaDescription",
      "bedrooms",
      "bathrooms",
      "furnished",
      "permitId",
      "unitNo",
      "govtIssuedQr",
      "projectId",
      "tagLine",
    ];

    const params = fields.map((field) => data[field] ?? null);

    const { rows } = await client.query(
      PropertyQueries.CREATE_OR_UPDATE_PROPERTY(fields.length),
      params
    );
    const property = rows[0] as PropertyDTO;

    if (!property?.id) {
      throw new Error("Property creation failed: ID is missing.");
    }

    // -------- Step 2: Insert Images --------
    if (Array.isArray(imageKeys) && imageKeys.length > 0) {
      const inserts = imageKeys.map((key) =>
        client.query(PropertyQueries.INSERT_PROPERTY_IMAGE, [
          property.id,
          key,
          statusId,
          48, // mediaTypeId
          loginId,
        ])
      );
      await Promise.all(inserts);
    }

    // -------- Step 3: Insert Features --------
    const validFeatures = Array.isArray(features)
      ? features
      : typeof features === "string"
      ? String(features)
          .split(",")
          .map((f) => f.trim())
          .filter(Boolean)
      : [];

    const inserts = [];

    for (const feature of validFeatures) {
      const { rows: existing } = await client.query(
        PropertyQueries.CHECK_EXISTING_FEATURE,
        [property.id, feature]
      );

      if (existing.length === 0) {
        inserts.push(
          client.query(PropertyQueries.INSERT_FEATURE, [
            property.id,
            feature,
            statusId,
            data.createdBy,
          ])
        );
      }
    }

    await Promise.all(inserts);

    return property;
  }

  async updateStatus(id: number, statusId: number): Promise<number> {
    const result = await db.query(PropertyQueries.UPDATE_PROPERTY_STATUS, [
      statusId,
      id,
    ]);
    return result.rowCount ?? 0;
  }

  async getPropertyFlag(id: number, column: string): Promise<any> {
    const result = await db.query(PropertyQueries.GET_PROPERTY_FLAG(column), [id]);
    return result.rows[0];
  }

  async updatePropertyFlag(id: number, column: string, newValue: boolean): Promise<number> {
  const result = await db.query(PropertyQueries.UPDATE_PROPERTY_FLAG(column), [newValue, id]);
  return result.rowCount ?? 0;
  }

  async deleteProperty(id: number): Promise<void> {
    const result = await db.query(PropertyQueries.DELETE_PROPERTY_BY_ID, [id]);
    if (result.rowCount === 0)
      throw new Error("Property not found or already deleted");
  }

  async updatePhotos(req: any): Promise<PropertyDTO | null> {
    return this.getPropertyById(Number(req.params.propertyId));
  }
}
