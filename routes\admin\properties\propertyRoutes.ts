import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import { AdminPropertyController } from "../../../controller/admin/properties/PropertyController";

const adminPropertyController = new AdminPropertyController();

const router = Router();
const upload = storageData("documents");

// GET all properties with pagination and filters
router.get("/", adminPropertyController.getAllProperties);

// GET property by ID
router.get("/:id", adminPropertyController.getPropertyById);

// PUT update property status
router.put("/:id/status", upload.none(), adminPropertyController.updateStatus);

// PUT toggle featured/verified flags
router.put("/:id/flag", upload.none(), adminPropertyController.toggleFlag);

// DELETE property
router.delete("/:id", adminPropertyController.deleteProperty);

// create a note for a property
router.post("/:id/notes", upload.none(), adminPropertyController.createNote);

// GET notes of a property
router.get("/note/:id", adminPropertyController.getNotes);

export default router;
