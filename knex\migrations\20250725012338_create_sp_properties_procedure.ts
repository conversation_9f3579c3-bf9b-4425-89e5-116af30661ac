import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.raw(`

    CREATE OR REPLACE FUNCTION create_or_update_property(
      p_id INTEGER,
      p_code TEXT,
      p_name TEXT,
      p_local TEXT,
      p_agency_id INTEGER,
      p_property_type_id INTEGER,
      p_apartment_type_id INTEGER,
      p_total_rooms INTEGER,
      p_location_id INTEGER,
      p_address TEXT,
      p_currency_id INTEGER,
      p_price NUMERIC,
      p_size NUMERIC,
      p_permit_no TEXT,
      p_parking BOOLEAN,
      p_swimming_pools BOOLEAN,
      p_gym BOOLEAN,
      p_start_date TIMESTAMP,
      p_status_id INTEGER,
      p_created_by INTEGER,
      p_is_featured BOOLEAN,
      p_is_verified BOOLEAN,
      p_admin_note TEXT,
      p_expiry_date TIMESTAMP,
      p_listing_type INTEGER,
      p_completion_status INTEGER,
      p_ownership_type_id INTEGER,
      p_slug TEXT,
      p_meta_title TEXT,
      p_meta_description TEXT,
      p_bedrooms INTEGER,
      p_bathrooms INTEGER,
      p_furnished BOOLEAN,
      p_permit_id TEXT,
      p_unit_no TEXT,
      p_govt_issued_qr TEXT,
      p_project_id TEXT,
      p_tagline TEXT
    )
    RETURNS TABLE (
      id INTEGER,
      code TEXT,
      name TEXT,
      price NUMERIC,
      size NUMERIC,
      status_id INTEGER,
      is_featured BOOLEAN,
      is_verified BOOLEAN
    ) AS $$
    BEGIN
      IF p_id IS NULL THEN
        RETURN QUERY
        INSERT INTO agn.properties (
          "code", "name", "local", "agencyId", "propertyTypeId", "apartmentTypeId",
          "totalRooms", "locationId", "address", "currencyId", "price", "size", "permitNo",
          "parking", "swimmingPools", "gym", "startDate", "statusId", "createdBy",
          "isFeatured", "isVerified", "adminNote", "expiryDate", "listingType",
          "completionStatus", "ownershipTypeId", "slug", "metaTitle", "metaDescription",
          "bedrooms", "bathrooms", "furnished", "permitId", "unitNo", "govtIssuedQr",
          "projectId", "tagLine", "createdOn"
        ) VALUES (
          p_code, p_name, p_local, p_agency_id, p_property_type_id, p_apartment_type_id,
          p_total_rooms, p_location_id, p_address, p_currency_id, p_price, p_size, p_permit_no,
          p_parking, p_swimming_pools, p_gym, p_start_date, p_status_id, p_created_by,
          p_is_featured, p_is_verified, p_admin_note, p_expiry_date, p_listing_type,
          p_completion_status, p_ownership_type_id, p_slug, p_meta_title, p_meta_description,
          p_bedrooms, p_bathrooms, p_furnished, p_permit_id, p_unit_no, p_govt_issued_qr,
          p_project_id, p_tagline, NOW()
        )
        RETURNING properties."id", properties."code", properties."name", properties."price", properties."size", properties."statusId", properties."isFeatured", properties."isVerified";
      ELSE
        RETURN QUERY
          UPDATE agn.properties SET
            "code" = p_code,
            "name" = p_name,
            "local" = p_local,
            "agencyId" = p_agency_id,
            "propertyTypeId" = p_property_type_id,
            "apartmentTypeId" = p_apartment_type_id,
            "totalRooms" = p_total_rooms,
            "locationId" = p_location_id,
            "address" = p_address,
            "currencyId" = p_currency_id,
            "price" = p_price,
            "size" = p_size,
            "permitNo" = p_permit_no,
            "parking" = p_parking,
            "swimmingPools" = p_swimming_pools,
            "gym" = p_gym,
            "startDate" = p_start_date,
            "statusId" = p_status_id,
            "isFeatured" = p_is_featured,
            "isVerified" = p_is_verified,
            "adminNote" = p_admin_note,
            "expiryDate" = p_expiry_date,
            "listingType" = p_listing_type,
            "completionStatus" = p_completion_status,
            "ownershipTypeId" = p_ownership_type_id,
            "slug" = p_slug,
            "metaTitle" = p_meta_title,
            "metaDescription" = p_meta_description,
            "bedrooms" = p_bedrooms,
            "bathrooms" = p_bathrooms,
            "furnished" = p_furnished,
            "permitId" = p_permit_id,
            "unitNo" = p_unit_no,
            "govtIssuedQr" = p_govt_issued_qr,
            "projectId" = p_project_id,
            "tagLine" = p_tagline,
            "modifiedBy" = p_created_by,
            "modifiedOn" = NOW()
          WHERE agn.properties."id" = p_id
        RETURNING properties."id", properties."code", properties."name", properties."price", properties."size", properties."statusId", properties."isFeatured", properties."isVerified";
      END IF;
    END;
    $$ LANGUAGE plpgsql;

    CREATE OR REPLACE FUNCTION get_filtered_properties(
        p_status_id INTEGER DEFAULT NULL,
        p_property_type_id INTEGER DEFAULT NULL,
        p_location_id INTEGER DEFAULT NULL,
        p_listing_type INTEGER DEFAULT NULL,
        p_limit INTEGER DEFAULT 10,
        p_page INTEGER DEFAULT 1
    )
    RETURNS TABLE (
        id INTEGER,
        name TEXT,
        price NUMERIC,
        size NUMERIC,
        location_name TEXT,
        property_type_name TEXT,
        listing_type INTEGER,
        status_id INTEGER,
        status_name TEXT,
        is_featured BOOLEAN,
        is_verified BOOLEAN,
        expiry_date TIMESTAMP,
        slug TEXT,
        meta_title TEXT,
        total_pages INTEGER,
        total_count INTEGER,
        images JSON
    ) AS $$
    DECLARE
        offset_val INTEGER := (p_page - 1) * p_limit;
        total_records INTEGER;
    BEGIN
    
    -- Count total rows for pagination
    SELECT COUNT(*) INTO total_records
    FROM agn.properties p
    WHERE 
        (p_status_id IS NULL OR p."statusId" = p_status_id) AND
        (p_property_type_id IS NULL OR p."propertyTypeId" = p_property_type_id) AND
        (p_location_id IS NULL OR p."locationId" = p_location_id) AND
        (p_listing_type IS NULL OR p."listingType" = p_listing_type);

    -- Main SELECT with exact column match
    RETURN QUERY
    SELECT
        p."id"::INTEGER,
        p."name"::TEXT,
        p."price"::NUMERIC,
        p."size"::NUMERIC,
        l."name"::TEXT AS location_name,
        pt."name"::TEXT AS property_type_name,
        p."listingType"::INTEGER AS listing_type,
        p."statusId"::INTEGER AS status_id,
        s."name"::TEXT AS status_name,
        p."isFeatured"::BOOLEAN AS is_featured,
        p."isVerified"::BOOLEAN AS is_verified,
        p."expiryDate"::TIMESTAMP AS expiry_date,
        p."slug"::TEXT,
        p."metaTitle"::TEXT AS meta_title,
        CEIL(total_records::DECIMAL / p_limit)::INTEGER AS total_pages,
        total_records::INTEGER AS total_count,
        COALESCE(imgs.images, '[]'::JSON) AS images
    FROM agn.properties p
    LEFT JOIN look.status s ON p."statusId" = s."id"
    LEFT JOIN list.location l ON p."locationId" = l."id"
    LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
    LEFT JOIN LATERAL (
      SELECT json_agg(json_build_object('id', i.id, 'url', i."imageUrl")) AS images
      FROM agn.images i
      WHERE i."propertyId" = p."id"
    ) imgs ON TRUE
    WHERE 
        (p_status_id IS NULL OR p."statusId" = p_status_id) AND
        (p_property_type_id IS NULL OR p."propertyTypeId" = p_property_type_id) AND
        (p_location_id IS NULL OR p."locationId" = p_location_id) AND
        (p_listing_type IS NULL OR p."listingType" = p_listing_type)
    ORDER BY p."createdOn" DESC
    OFFSET offset_val
    LIMIT p_limit;
    END;
    $$ LANGUAGE plpgsql;

    CREATE OR REPLACE FUNCTION get_property_detail(p_id INTEGER)
      RETURNS TABLE (
        id INTEGER,
        code TEXT,
        name TEXT,
        local TEXT,
        price NUMERIC,
        size NUMERIC,
        slug TEXT,
        permit_no TEXT,
        permit_id TEXT,
        unit_no TEXT,
        address TEXT,
        bedrooms INTEGER,
        bathrooms INTEGER,
        total_rooms INTEGER,
        furnished BOOLEAN,
        parking BOOLEAN,
        swimming_pools BOOLEAN,
        gym BOOLEAN,
        start_date TIMESTAMP,
        expiry_date TIMESTAMP,
        is_featured BOOLEAN,
        is_verified BOOLEAN,
        admin_note TEXT,
        meta_title TEXT,
        meta_description TEXT,
        tag_line TEXT,
        govt_issued_qr TEXT,
        created_on TIMESTAMP,
        modified_on TIMESTAMP,
        status JSON,
        agency JSON,
        location JSON,
        property_type JSON,
        apartment_type JSON,
        ownership_type JSON,
        currency JSON,
        listing_type JSON,
        completion_status JSON,
        images JSON,
        features TEXT[]
      )
      AS $$
      BEGIN
        RETURN QUERY
        SELECT
          p."id",
          p."code"::TEXT,
          p."name"::TEXT,
          p."local"::TEXT,
          p."price",
          p."size",
          p."slug"::TEXT,
          p."permitNo"::TEXT,
          p."permitId"::TEXT,
          p."unitNo"::TEXT,
          p."address"::TEXT,
          p."bedrooms",
          p."bathrooms",
          p."totalRooms",
          p."furnished",
          p."parking",
          p."swimmingPools",
          p."gym",
          p."startDate"::TIMESTAMP,
          p."expiryDate"::TIMESTAMP,
          p."isFeatured",
          p."isVerified",
          p."adminNote"::TEXT,
          p."metaTitle"::TEXT,
          p."metaDescription"::TEXT,
          p."tagLine"::TEXT,
          p."govtIssuedQr"::TEXT,
          p."createdOn"::TIMESTAMP,
          p."modifiedOn"::TIMESTAMP,

          json_build_object('id', s."id", 'name', s."name")::JSON,
          json_build_object('id', a."id", 'name', a."name")::JSON,
          json_build_object('id', l."id", 'name', l."name")::JSON,
          json_build_object('id', pt."id", 'name', pt."name")::JSON,
          json_build_object('id', at."id", 'name', at."name")::JSON,
          json_build_object('id', ot."id", 'name', ot."name")::JSON,
          json_build_object('id', cur."id", 'name', cur."name")::JSON,
          json_build_object('id', lt."id", 'name', lt."name")::JSON,
          json_build_object('id', cs."id", 'name', cs."name")::JSON,

          (
            SELECT json_agg(json_build_object('id', img."id", 'url', img."imageUrl"))
            FROM agn.images img
            WHERE img."propertyId" = p."id"
          )::JSON,

          (
            SELECT ARRAY_AGG(DISTINCT f."featureName")
            FROM agn.features f
            WHERE f."propertyId" = p."id"
          )

        FROM agn.properties p
        LEFT JOIN look.status s ON p."statusId" = s."id"
        LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
        LEFT JOIN list.location l ON p."locationId" = l."id"
        LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
        LEFT JOIN look.type at ON p."apartmentTypeId" = at."id"
        LEFT JOIN look.type ot ON p."ownershipTypeId" = ot."id"
        LEFT JOIN list.currency cur ON p."currencyId" = cur."id"
        LEFT JOIN look.type lt ON p."listingType" = lt."id"
        LEFT JOIN look.type cs ON p."completionStatus" = cs."id"
        WHERE p."id" = p_id;
      END;
      $$ LANGUAGE plpgsql;

      CREATE OR REPLACE FUNCTION get_property_status_counts(
        p_property_type_id INTEGER DEFAULT NULL,
        p_location_id INTEGER DEFAULT NULL,
        p_listing_type INTEGER DEFAULT NULL
      )
      RETURNS TABLE (
        status_id INTEGER,
        status_name TEXT,
        count INTEGER
      )
      AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          s.id AS status_id,
          s.name AS status_name,
          COUNT(p.id)::INTEGER AS count
        FROM agn.properties p
        JOIN look.status s ON p."statusId" = s.id
        WHERE 
          (p_property_type_id IS NULL OR p."propertyTypeId" = p_property_type_id) AND
          (p_location_id IS NULL OR p."locationId" = p_location_id) AND
          (p_listing_type IS NULL OR p."listingType" = p_listing_type)
        GROUP BY s.id, s.name
        ORDER BY s.name;
      END;
      $$ LANGUAGE plpgsql;

  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw(`DROP FUNCTION IF EXISTS 
    create_or_update_property(
      INTEGER, TEXT, TEXT, TEXT, INTEGER, INTEGER, INTEGER, INTEGER, INTEGER, TEXT, 
      INTEGER, NUMERIC, NUMERIC, TEXT, BOOLEAN, BOOLEAN, BOOLEAN, TIMESTAMP, INTEGER, INTEGER, 
      BOOLEAN, BOOLEAN, TEXT, TIMESTAMP, INTEGER, INTEGER, INTEGER, TEXT, TEXT, TEXT, 
      INTEGER, INTEGER, BOOLEAN, TEXT, TEXT, TEXT, TEXT, TEXT
    ),
    get_property_detail(INTEGER),
    get_filtered_properties(INTEGER, INTEGER, INTEGER, INTEGER, INTEGER, INTEGER),
    get_property_status_counts(INTEGER, INTEGER, INTEGER);
  `);
}
